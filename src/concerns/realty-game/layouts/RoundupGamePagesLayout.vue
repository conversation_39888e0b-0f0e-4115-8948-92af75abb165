<template>
  <div class="roundup-game-pages-layout">
    <!-- Property Header for all property-specific routes -->
    <div
      v-if="showPropertyHeader && currentPropertyData"
      class="layout-rugpage-prop-header q-mb-lg q-px-sm"
      :class="{ clickable: $route.name !== 'rRoundupGameProperty' }"
      @click="handleHeaderClick"
    >
      <div class="row items-center justify-between">
        <div class="header-info col-auto q-pa-md">
          <h1 class="text-h4 text-weight-bold text-primary q-my-xs">
            {{ currentPropertyData.title }}
          </h1>
          <p
            class="text-body1 text-grey-7"
            v-if="currentPropertyData.street_address"
          >
            {{ currentPropertyData.street_address }}
          </p>
          <p class="text-body1 text-grey-7" v-else>
            {{ currentPropertyData.city }},
            {{ currentPropertyData.postal_code || currentPropertyData.region }}
          </p>
          <div class="text-caption text-grey-6 q-mt-xs" v-if="gameTitle">
            Part of {{ gameTitle }}
          </div>
        </div>

        <div class="header-thumbnail col-auto mobile-hide">
          <div class="thumbnail-container" v-if="propertyImages.length > 0">
            <img
              :src="propertyImages[0].image_details.url"
              :alt="currentPropertyData.title"
              class="property-thumbnail"
            />
            <div class="thumbnail-overlay">
              <!-- <q-icon name="photo_camera" size="sm" color="white" />
              <span class="text-caption text-white q-ml-xs">
                {{ propertyImages.length }}
              </span> -->
            </div>
          </div>
          <div class="thumbnail-placeholder" v-else>
            <q-icon name="home" size="lg" color="grey-5" />
          </div>
        </div>
      </div>
    </div>
    <router-view
      :currPlayerSssnId="currPlayerSssnId"
      :game-session-id="routeSssnId"
      :listing-in-game-uuid="listingInGameUuid"
      :game-communities-details="gameCommunitiesDetails"
      :shareable-results-url="shareableResultsUrl"
      :is-current-user-session="isCurrentUserSession"
      :game-title="gameTitle"
      :game-default-currency="gameDefaultCurrency"
      :total-properties="totalProperties"
      :first-prop-listing="firstPropListing"
      :realty-game-summary="realtyGameSummary"
      :is-loading="isLoading"
      :error="error"
      :results="results"
      :player-results="playerResults"
      :ss-game-session="ssGameSession"
      :comparison-summary="comparisonSummary"
      :game-breakdown="gameBreakdown"
      :overall-ranking="overallRanking"
      :leaderboard="leaderboard"
      :show-leaderboard="showLeaderboard"
      :get-score-color="getScoreColor"
      :format-price-with-both-currencies="formatPriceWithBothCurrencies"
      :current-property-data="currentPropertyData"
      :is-property-loading="isPropertyLoading"
      :property-error="propertyError"
      @load-results="handleLoadResults"
      @update-progress="handleProgressUpdate"
      @game-complete="handleGameComplete"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useSingleListingRealtyGame } from 'src/concerns/realty-game/composables/useSingleListingRealtyGame'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import { useServerSingleListingGameResults } from 'src/concerns/realty-game/composables/useServerSingleListingGameResults'
import { useCurrencyConverter } from 'src/concerns/realty-game/composables/useCurrencyConverter'
import { useRealtyGameStore } from 'src/stores/realtyGame'

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

// Initialize composables
const {
  totalProperties,
  gameTitle,
  realtyGameSummary,
  gameCommunitiesDetails,
  firstPropListing,
  fetchPriceGuessData,
  gameDefaultCurrency,
  setRealtyGameData,
} = useSingleListingRealtyGame()

const { getCurrentSessionId, getCurrencySelection } = useRealtyGameStorage()

// Initialize stores
const realtyGameStore = useRealtyGameStore()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
  realtyGameListingDetails,
} = useServerSingleListingGameResults()

const { setCurrency, formatPriceWithBothCurrencies } = useCurrencyConverter()

// Additional state for property data
const currentPropertyData = ref(null)
const isPropertyLoading = ref(false)
const propertyError = ref(null)

// Computed properties
const routeSssnId = computed(
  () => $route.query.session || $route.params.routeSssnId || ''
)

const listingInGameUuid = computed(() => $route.params.listingInGameUuid || '')

// Show property header for all routes that have listingInGameUuid parameter
const showPropertyHeader = computed(() => {
  return !!listingInGameUuid.value
})

// Property images computed property
const propertyImages = computed(() => {
  if (!currentPropertyData.value?.sale_listing_pics) return []
  return currentPropertyData.value.sale_listing_pics.filter(
    (image) => !image.flag_is_hidden
  )
})

const shareableResultsUrl = computed(() => {
  if (!routeSssnId.value || !$route.params.gameSlug) {
    return ''
  }
  let shareRoute = {
    name: 'rRoundupGameResultsShareable',
    params: {
      gameSlug: $route.params.gameSlug,
      listingInGameUuid: listingInGameUuid.value,
      routeSssnId: routeSssnId.value,
    },
  }
  let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
  return fullPath
})

const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === routeSssnId.value
})

const currPlayerSssnId = computed(() => {
  return getCurrentSessionId()
})

// Results-specific computed properties
const overallRanking = computed(() => results.value?.overall_ranking || null)
const leaderboard = computed(() => results.value?.leaderboard || [])
const showLeaderboard = computed(() => {
  return leaderboard.value && leaderboard.value.length > 1
})

// Event handlers
const handleLoadResults = async () => {
  if (routeSssnId.value && $route.params.listingInGameUuid) {
    await fetchResults(routeSssnId.value, $route.params.listingInGameUuid)
  }
}

const handleProgressUpdate = (data) => {
  console.log('Progress update:', data)
}

const handleGameComplete = (sessionId) => {
  $router.push({
    name: 'rRoundupGameResultsSummary',
    params: {
      gameSlug: $route.params.gameSlug,
      listingInGameUuid: listingInGameUuid.value,
      routeSssnId: sessionId,
    },
  })
}

// New click handler for property header
const handleHeaderClick = () => {
  if ($route.name !== 'rRoundupGameProperty') {
    $router.push({
      name: 'rRoundupGameProperty',
      params: {
        gameSlug: $route.params.gameSlug,
        listingInGameUuid: listingInGameUuid.value,
      },
    })
  }
}

// Initialize currency from session
const initializeCurrency = () => {
  const sessionCurrency = getCurrencySelection(routeSssnId.value)
  if (sessionCurrency) {
    setCurrency(sessionCurrency)
  }
}

// Fetch property data
const fetchPropertyData = async (listingInGameUuid) => {
  if (!listingInGameUuid) return

  isPropertyLoading.value = true
  propertyError.value = null

  try {
    const response = await axios.get(
      `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${listingInGameUuid}`
    )
    currentPropertyData.value = response.data.sale_listing
  } catch (err) {
    console.error('Failed to load property data:', err)
    propertyError.value = 'Failed to load property data'
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error',
    })
  } finally {
    isPropertyLoading.value = false
  }
}

// Initialize game data
const initializeGame = async () => {
  try {
    if ($route.params.gameSlug) {
      await fetchPriceGuessData($route.params.gameSlug)
    }
  } catch (err) {
    console.error('Failed to load game data:', err)
    $q.notify({
      color: 'negative',
      message: 'Failed to load game data',
      icon: 'error',
    })
  }
}

// Lifecycle
onMounted(async () => {
  if (realtyGameStore.isDataLoaded && realtyGameStore.gameListings.length > 0) {
    console.log('Using preFetched data from store')
    setRealtyGameData({
      properties: realtyGameStore.gameListings,
      gameTitle: realtyGameStore.gameTitle,
      gameDesc: realtyGameStore.gameDesc,
      gameBgImageUrl: realtyGameStore.gameBgImageUrl,
      gameDefaultCurrency: realtyGameStore.gameDefaultCurrency,
    })

    if (realtyGameStore.currentProperty) {
      currentPropertyData.value = realtyGameStore.currentProperty
    }
  } else {
    await initializeGame()
  }

  if (listingInGameUuid.value && !currentPropertyData.value) {
    await fetchPropertyData(listingInGameUuid.value)
  }

  initializeCurrency()

  const resultsRoutes = [
    'rRoundupGameResultsSummary',
    'rRoundupGameResultsShareable',
    'rRoundupGameResultsDetailed',
  ]

  if (resultsRoutes.includes($route.name) && routeSssnId.value) {
    await handleLoadResults()
  }
})

// Watch for route changes to reload results and fetch property data
watch(
  () => [
    $route.name,
    routeSssnId.value,
    $route.params.gameSlug,
    listingInGameUuid.value,
  ],
  async ([routeName, sessionId, gameSlug, newListingInGameUuid]) => {
    const resultsRoutes = [
      'rRoundupGameResultsSummary',
      'rRoundupGameResultsShareable',
      'rRoundupGameResultsDetailed',
    ]

    if (resultsRoutes.includes(routeName) && sessionId && gameSlug) {
      await handleLoadResults()
    }

    if (newListingInGameUuid) {
      await fetchPropertyData(newListingInGameUuid)
    }
  }
)
</script>

<script>
export async function preFetch({ currentRoute, ssrContext }) {
  console.log(
    'RoundupGamePagesLayout preFetch called! Route:',
    currentRoute.path,
    'Params:',
    currentRoute.params
  )

  const gameSlug = currentRoute.params.gameSlug
  const listingInGameUuid = currentRoute.params.listingInGameUuid

  if (!gameSlug) {
    console.warn('Missing gameSlug in preFetch')
    return
  }

  const { default: axios } = await import('axios')
  const { pwbFlexConfig } = await import('boot/pwb-flex-conf')
  const { useRealtyGameStore } = await import('src/stores/realtyGame')

  try {
    const requests = [
      axios.get(
        `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
      ),
    ]

    if (listingInGameUuid) {
      requests.push(
        axios.get(
          `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${listingInGameUuid}`
        )
      )
    }

    const responses = await Promise.all(requests)
    const gameResponse = responses[0]
    const propertyResponse = responses[1]

    if (gameResponse.data) {
      const gameData = gameResponse.data.price_guess_inputs
      const realtyGameDetails = gameResponse.data.realty_game_details
      const gameListings =
        gameData?.game_listings?.filter(
          (game) => game.listing_details.visible === true
        ) || []

      const store = useRealtyGameStore()

      const storeData = {
        gameListings: gameListings.map((game) => game.listing_details),
        gameTitle: realtyGameDetails?.game_title || 'Property Price Challenge',
        gameDesc: realtyGameDetails?.game_description || '',
        gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
        gameDefaultCurrency: gameData?.default_currency || 'GBP',
        totalProperties: gameListings.length,
        isDataLoaded: true,
      }

      if (propertyResponse?.data) {
        storeData.currentProperty = propertyResponse.data.sale_listing
        storeData.listingInGameUuid = listingInGameUuid
      }

      store.setRealtyGameData(storeData)

      return {
        gameData: gameResponse.data,
        propertyData: propertyResponse?.data || null,
      }
    }
  } catch (error) {
    console.error('RoundupGamePagesLayout preFetch error:', error)
    return null
  }
}

export default {
  name: 'RoundupGamePagesLayout',
  preFetch,
}
</script>

<style scoped>
.roundup-game-pages-layout {
  min-height: 100vh;
}

.layout-rugpage-prop-header {
  background: white; /* Matches .rug-prop-header background */
  border-radius: 12px; /* Matches .rug-prop-header border-radius */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* Matches .rug-prop-header box-shadow */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.layout-rugpage-prop-header.clickable {
  cursor: pointer;
  transition: background-color 0.2s;
}

.layout-rugpage-prop-header.clickable:hover {
  background: rgba(245, 245, 245, 0.95); /* Slightly lighter hover effect */
}

.header-thumbnail {
  margin-left: 1rem; /* Matches .rug-prop-header spacing */
}

.thumbnail-container {
  position: relative;
  width: 120px; /* Matches .rug-prop-header thumbnail size */
  height: 120px;
  border-radius: 12px; /* Matches .rug-prop-header border-radius */
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Matches .rug-prop-header shadow */
}

.property-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Matches .rug-prop-header object-fit */
}

.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    transparent,
    rgba(0, 0, 0, 0.7)
  ); /* Matches .rug-prop-header overlay */
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-placeholder {
  width: 120px; /* Matches thumbnail-container size */
  height: 120px;
  border-radius: 12px;
  background: #f5f5f5; /* Matches .rug-prop-header placeholder background */
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd; /* Matches .rug-prop-header placeholder border */
}

@media (max-width: 768px) {
  .mobile-hide {
    display: none;
  }

  .layout-rugpage-prop-header .row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem; /* Matches .rug-prop-header mobile styling */
  }
}
</style>
