<template>
  <div class="realty-game-start-page">
    <!-- The <q-no-ssr> wrapper has been removed to enable Server-Side Rendering -->
    <div class="max-ctr q-pa-sm rgsp q-mb-xl q-pb-xl">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-container text-center q-pa-xl">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md text-h6">Loading Properties...</div>
        <div class="text-body2 text-grey-7">
          Preparing your price guessing challenge
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container text-center q-pa-xl">
        <q-icon name="error" color="negative" size="3em" />
        <div class="q-mt-md text-h6 text-negative">
          Failed to Load Properties
        </div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary" label="Try Again" @click="initializeGame" />
      </div>

      <!-- No Properties State -->
      <div
        v-else-if="storeTotalProperties < 1"
        class="loading-container text-center q-pa-xl"
      >
        <div class="q-mt-md text-h6">
          Sorry, this price guessing challenge is not available yet
        </div>
        <div class="text-body2 text-grey-7">Please check again later</div>
      </div>

      <!-- Game Start -->
      <div v-else class="game-start-container">
        <!-- Welcome Section -->
        <div
          class="welcome-section text-center q-mb-xl"
          :style="backgroundStyle"
        >
          <!-- Background overlay for better text readability -->
          <div class="game-overlay">
            <div class="welcome-icon q-mb-lg">
              <img
                class="hpg-logo-img"
                :src="logoUrl"
                alt="HousePriceGuess Logo"
              />
            </div>
            <h1 class="text-h3 text-weight-bold text-white q-mb-md">
              {{ storeGameTitle }}
            </h1>
            <p class="text-h6 text-white q-mb-lg">
              Test your property valuation skills with
              {{ storeTotalProperties }} real properties
            </p>
            <div class="challenge-stats">
              <div class="row q-col-gutter-md justify-center">
                <div class="col-auto">
                  <q-chip color="primary" text-color="white" icon="home">
                    {{ storeTotalProperties }} Properties
                  </q-chip>
                </div>
                <div class="col-auto">
                  <q-chip color="secondary" text-color="white" icon="timer">
                    ~{{ Math.ceil(storeTotalProperties * 0.5) }} minutes
                  </q-chip>
                </div>
                <div class="col-auto">
                  <q-chip
                    color="positive"
                    text-color="white"
                    icon="emoji_events"
                  >
                    Score up to {{ storeTotalProperties * 100 }} points
                  </q-chip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- How It Works -->
        <q-card class="how-it-works-card q-mb-xl" flat bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h5 text-weight-medium text-center q-mb-lg">
              <q-icon name="help_outline" color="primary" class="q-mr-sm" />
              How It Works
            </div>
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-4">
                <div class="step-card text-center">
                  <div class="step-number">1</div>
                  <div class="step-title">View Property</div>
                  <div class="step-description">
                    See photos and details of real properties currently for sale
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-4">
                <div class="step-card text-center">
                  <div class="step-number">2</div>
                  <div class="step-title">Make Your Guess</div>
                  <div class="step-description">
                    Estimate the asking price based on what you see
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-4">
                <div class="step-card text-center">
                  <div class="step-number">3</div>
                  <div class="step-title">Get Your Score</div>
                  <div class="step-description">
                    See how close you were and compare with other players
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- User Info Collection -->
        <q-card class="user-info-card q-mb-xl" flat bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 text-weight-medium q-mb-md">
              <q-icon name="person" color="primary" class="q-mr-sm" />
              Player Information
            </div>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="playerName"
                  label="Your name"
                  outlined
                  dense
                  placeholder="Enter your name or leave blank"
                  hint="This helps us show you how you compare to other players"
                >
                  <template v-slot:prepend>
                    <q-icon name="person" />
                  </template>
                </q-input>
              </div>
              <div class="col-12 col-md-6 currency-selector">
                <q-select
                  v-model="selectedCurrency"
                  :options="currencyOptions"
                  option-label="label"
                  option-value="code"
                  emit-value
                  map-options
                  outlined
                  dense
                  label="Currency"
                  hint="Choose your preferred currency for prices"
                >
                  <template v-slot:prepend>
                    <q-icon name="currency_exchange" />
                  </template>
                  <template v-slot:option="scope">
                    <q-item v-bind="scope.itemProps">
                      <q-item-section avatar>
                        <span class="currency-symbol">{{
                          scope.opt.symbol
                        }}</span>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.name }}</q-item-label>
                        <q-item-label caption>{{
                          scope.opt.code
                        }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  <template v-slot:selected>
                    <span class="selected-currency" v-if="selectedCurrencyData">
                      {{ selectedCurrencyData.symbol }}
                      {{ selectedCurrencyData.name }}
                    </span>
                  </template>
                </q-select>
              </div>
            </div>
            <div class="text-caption text-grey-6 q-mt-md">
              Your guesses will be saved and compared with other players. No
              personal information is required.
            </div>
          </q-card-section>
        </q-card>

        <!-- Start Button or Results Section -->
        <div class="start-section text-center q-mb-xl">
          <!-- Show results section if game is completed -->
          <div v-if="isGameCompleted" class="completed-game-section">
            <div class="completion-message q-mb-lg">
              <q-icon
                name="emoji_events"
                color="positive"
                size="3em"
                class="q-mb-md"
              />
              <h2 class="text-h5 text-weight-bold text-positive q-mb-none">
                Challenge Completed!
              </h2>
              <!-- <p class="text-body1 text-grey-7 q-mb-lg">
                You've already completed this challenge with {{ completedGuessCount }} property guesses.
              </p> -->
            </div>

            <div class="completed-actions">
              <q-btn
                color="primary"
                size="lg"
                rounded
                unelevated
                icon="assessment"
                label="View Your Results"
                @click="viewResults"
                class="q-mb-md q-mr-md"
              />
              <q-btn
                color="secondary"
                size="lg"
                rounded
                outline
                icon="play_arrow"
                label="Review your game"
                @click="startGame"
                class="q-mb-md"
              />
              <!-- <q-btn color="secondary"
                     size="lg"
                     rounded
                     outline
                     icon="share"
                     label="Share Results"
                     @click="shareResults"
                     class="q-mb-md" /> -->
            </div>

            <div class="invite-others q-mt-lg">
              <p class="text-body1 text-grey-8 q-mb-md">
                Share to Challenge your friends and see how they compare!
              </p>

              <SocialSharing
                socialSharingPrompt=""
                :hideSharingPrompt="true"
                socialSharingTitle="Check out how I did in this property price game"
                :urlProp="shareableResultsUrl"
              >
              </SocialSharing>
              <!-- <q-btn color="accent"
                     size="md"
                     rounded
                     outline
                     icon="group_add"
                     label="Invite Others to Play"
                     @click="inviteOthers"
                     class="invite-button" /> -->
            </div>
          </div>

          <!-- Show start button if game is not completed -->
          <div v-else class="new-game-section">
            <q-btn
              color="primary"
              size="xl"
              rounded
              unelevated
              icon="play_arrow"
              label="Start Challenge"
              @click="startGame"
              class="start-button"
            />
            <div class="text-caption text-grey-6 q-mt-md">
              Click to begin your property price challenge
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
import { usePlayerName } from '../composables/usePlayerName'
import { useRealtyGameStore } from 'src/stores/realtyGame'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'

// Import logo image
// import logoUrl from '@/assets/hpg_logo_july_2025.png'
import logoUrl from '/icons/favicon-128x128.png'

// Helper function to fetch and process data, used by both preFetch and client-side logic
const fetchAndSetGameData = async (gameSlug, store) => {
  const response = await axios.get(
    `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
  )

  if (response.data) {
    const gameData = response.data.price_guess_inputs
    const realtyGameDetails = response.data.realty_game_details
    const gameListings =
      gameData?.game_listings?.filter(
        (game) => game.listing_details.visible === true
      ) || []

    const storeData = {
      gameListings: gameListings,
      gameTitle: realtyGameDetails?.game_title || 'Property Price Challenge',
      gameDesc: realtyGameDetails?.game_description || '',
      gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
      gameDefaultCurrency: gameData?.default_currency || 'GBP',
      totalProperties: gameListings.length,
      currentProperty: null,
      isDataLoaded: true, // Flag to indicate data is ready
    }

    store.setRealtyGameData(storeData)
    return storeData
  }
  return null
}

export default {
  name: 'RealtyGameStartPage',
  components: {
    SocialSharing
  },
  // Props are removed because this page-level component is now self-sufficient,
  // fetching its own data based on the route. This is a more robust pattern for SSR.
  props: {
    shareableResultsUrl: {
      type: String,
      required: true,
    }
  },
  // preFetch hook for SSR and data prefetching
  async preFetch({ currentRoute, ssrContext }) {
    console.log('preFetch running on server for RealtyGameStartPage...')
    const gameSlug = currentRoute.params.gameSlug
    if (!gameSlug) return

    try {
      const store = useRealtyGameStore()
      const metaStore = useRealtyGameMetaStore()
      const gameData = await fetchAndSetGameData(gameSlug, store)
      // Set meta tags in the meta store
      console.log(`setting metastore with ${gameData.gameTitle}`)

      if (gameData) {
        metaStore.setMeta({
          title: `Start ${gameData.gameTitle} - Test Your Property Knowledge`,
          description:
            gameData.gameDesc ||
            'Test your property market knowledge with our interactive price guessing game.',
          image:
            gameData.gameBgImageUrl ||
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          url: `https://yourdomain.com${currentRoute.fullPath}`,
          keywords: `property price game, real estate challenge, property valuation, house price quiz, market knowledge test, ${gameData.gameTitle || ''
            }`,
        })
      }
      console.log(`MetaStore title now: ${metaStore.title}`)

      if (ssrContext) {
        ssrContext.realtyGameData = gameData
      }
    } catch (error) {
      console.error('preFetch error for start page:', error)
      // You could set an error state in the store here
    }
  },

  setup() {
    const $router = useRouter()
    const $route = useRoute()
    const $q = useQuasar()
    const realtyGameStore = useRealtyGameStore()
    // const metaStore = useRealtyGameMetaStore()

    // Local state for UI (loading, errors)
    const isLoading = ref(false)
    const error = ref(null)

    // Computed properties to read from the Pinia store
    const storeGameTitle = computed(() => realtyGameStore.gameTitle)
    const storeTotalProperties = computed(
      () => realtyGameStore.getTotalProperties
    )
    const storeBgImageUrl = computed(() => realtyGameStore.gameBgImageUrl)
    const firstPropListing = computed(() => realtyGameStore.firstVisibleListing)
    const storeDefaultCurrency = computed(
      () => realtyGameStore.gameDefaultCurrency
    )

    // --- Composables ---
    const {
      getOrCreateSessionId,
      saveSessionData,
      getSessionData,
      saveCurrencySelection,
      getCurrencySelection,
      getCurrentSessionGuesses,
    } = useRealtyGameStorage()
    const {
      selectedCurrency,
      availableCurrencies,
      selectedCurrencyData,
      setCurrency,
    } = useCurrencyConverter()
    const {
      playerName,
      updatePlayerName,
      promptForPlayerName,
      initializePlayerName,
    } = usePlayerName()

    // --- Game Initialization ---
    const gameSlug = computed(() => $route.params.gameSlug)

    const initializeGame = async () => {
      if (!gameSlug.value) return
      isLoading.value = true
      error.value = null
      try {
        await fetchAndSetGameData(gameSlug.value, realtyGameStore)
      } catch (err) {
        error.value = err.message || 'An unknown error occurred.'
        $q.notify({
          color: 'negative',
          message: 'Failed to load property data',
          icon: 'error',
        })
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      // If data wasn't loaded by preFetch (e.g., client-side navigation), load it now.
      if (!realtyGameStore.isDataLoaded) {
        initializeGame()
      }

      // Initialize player name from storage
      initializePlayerName()

      // Initialize currency
      const sessionData = getSessionData()
      const storedCurrency = getCurrencySelection()
      setCurrency(
        sessionData.selectedCurrency ||
        storedCurrency ||
        storeDefaultCurrency.value
      )
    })

    // --- Watchers ---
    watch(playerName, (newValue) => {
      updatePlayerName(newValue)
    })

    watch(selectedCurrency, (newValue) => {
      if (newValue) {
        saveCurrencySelection(newValue)
        saveSessionData({ selectedCurrency: newValue })
      }
    })

    // --- Computed Properties for Template ---
    const currencyOptions = computed(() =>
      availableCurrencies.value.map((currency) => ({
        code: currency.code,
        name: currency.name,
        symbol: currency.symbol,
        label: `${currency.symbol} ${currency.name} (${currency.code})`,
      }))
    )

    // Game completion logic
    const currentSessionGuesses = computed(() => getCurrentSessionGuesses())
    // Get the UUIDs of properties in the current game
    const gamePropertyUuids = computed(() =>
      realtyGameStore.gameListings.map(listing => listing.listing_details.uuid)
    )
    // const completedGuessCount = computed(() => Object.keys(currentSessionGuesses.value).length)
    // 9 July 2025 : above would result in false positives if the user has played multiple games
    // Below is slightly better but eventually I might need to implement a
    // proper solution where I set a game-specific property to mark when
    // game is completed.
    // Only count guesses for properties in the current game
    const filteredGuesses = computed(() => {
      const guesses = currentSessionGuesses.value
      const uuids = gamePropertyUuids.value
      return Object.keys(guesses).filter(uuid => uuids.includes(uuid))
    })
    const completedGuessCount = computed(() => filteredGuesses.value.length)
    const isGameCompleted = computed(() => {
      const totalProps = storeTotalProperties.value
      const guessCount = completedGuessCount.value
      return totalProps > 0 && guessCount >= totalProps
    })

    const backgroundStyle = computed(() => {
      const baseStyle = {
        padding: '0px',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }
      if (storeBgImageUrl.value) {
        return {
          ...baseStyle,
          backgroundImage: `url(${storeBgImageUrl.value})`,
        }
      }
      // Fallback gradient
      const gradients = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      ]
      const index = gameSlug.value
        ? gameSlug.value.charCodeAt(0) % gradients.length
        : 0
      return { ...baseStyle, background: gradients[index] }
    })

    // --- Methods ---
    const proceedWithGame = () => {
      const routeSssnId = getOrCreateSessionId()
      saveSessionData({
        playerName: playerName.value || 'Anonymous Player',
        totalProperties: storeTotalProperties.value,
        gameTitle: storeGameTitle.value,
        selectedCurrency: selectedCurrency.value,
        startedAt: new Date().toISOString(),
      })
      saveCurrencySelection(selectedCurrency.value)

      if (firstPropListing.value) {
        $router.push({
          name: 'rPriceGameProperty',
          params: {
            gameSlug: gameSlug.value,
            propertyUuid: firstPropListing.value.listing_details.uuid,
          },
          query: { session: routeSssnId },
        })
      } else {
        $q.notify({
          color: 'negative',
          message: 'No properties available for this game',
          icon: 'error',
        })
      }
    }

    const startGame = async () => {
      try {
        await promptForPlayerName({
          title: 'Choose Your Player Name',
          okLabel: "Let's Go!",
          allowCancel: false
        })
        proceedWithGame()
      } catch (error) {
        // If there's an error, proceed with current name
        proceedWithGame()
      }
    }

    // Methods for completed game actions
    const viewResults = () => {
      const sessionId = getCurrentSessionId()
      if (sessionId) {
        $router.push({
          name: 'rPriceGameResults',
          params: {
            gameSlug: gameSlug.value,
            routeSssnId: sessionId,
          },
        })
      }
    }

    const shareResults = () => {
      const sessionId = getCurrentSessionId()
      if (sessionId) {
        const shareUrl = `${window.location.origin}${$router.resolve({
          name: 'rPriceGameResultsShareable',
          params: {
            gameSlug: gameSlug.value,
            routeSssnId: sessionId,
          },
        }).href}`

        const shareText = `I completed the ${storeGameTitle.value} challenge! Check out my results and try it yourself.`

        if (navigator.share) {
          navigator.share({
            title: `${storeGameTitle.value} Results`,
            text: shareText,
            url: shareUrl,
          }).catch(() => {
            fallbackShare(shareUrl, shareText)
          })
        } else {
          fallbackShare(shareUrl, shareText)
        }
      }
    }

    const inviteOthers = () => {
      const gameUrl = `${window.location.origin}${$router.resolve({
        name: 'rPriceGameStart',
        params: {
          gameSlug: gameSlug.value,
        },
      }).href}`

      const inviteText = `Try the ${storeGameTitle.value} challenge! Test your property knowledge and see how you compare.`

      if (navigator.share) {
        navigator.share({
          title: storeGameTitle.value,
          text: inviteText,
          url: gameUrl,
        }).catch(() => {
          fallbackShare(gameUrl, inviteText)
        })
      } else {
        fallbackShare(gameUrl, inviteText)
      }
    }

    const fallbackShare = (url, text) => {
      navigator.clipboard.writeText(`${text}\n${url}`).then(() => {
        $q.notify({
          type: 'positive',
          message: 'Link copied to clipboard!',
          position: 'top',
          timeout: 3000,
        })
      }).catch(() => {
        $q.notify({
          type: 'info',
          message: 'Share this link: ' + url,
          position: 'top',
          timeout: 5000,
        })
      })
    }

    return {
      isLoading,
      error,
      playerName,
      selectedCurrency,
      selectedCurrencyData,
      currencyOptions,
      storeGameTitle,
      storeTotalProperties,
      backgroundStyle,
      initializeGame,
      startGame,
      logoUrl, // Expose logoUrl to the template
      // Game completion properties and methods
      isGameCompleted,
      completedGuessCount,
      viewResults,
      shareResults,
      inviteOthers,
    }
  },
}
</script>

<style scoped>
/* Your existing styles are fine and have been kept */
.game-overlay {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  padding: 32px;
  height: 100%;
  border-radius: 12px;
}

@media (max-width: 768px) {
  .game-overlay {
    padding: 24px;
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .game-overlay {
    padding: 20px;
    min-height: 450px;
  }
}

.realty-game-start-page {
  min-height: 100vh;
}

.max-ctr {
  max-width: 800px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.game-start-container {
  padding-top: 2rem;
}

.welcome-section {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
  border-radius: 50%;
  width: 110px;
  height: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  /* Subtle background for logo */
}

.hpg-logo-img {
  height: 70px;
  /* Slightly larger than the original 60px from PriceGuessGameLayout.vue */
  width: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  /* Slightly stronger shadow for depth */
  background: #fff;
  padding: 8px;
  /* Padding to match the white background effect */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hpg-logo-img:hover {
  transform: scale(1.1) rotate(-3deg);
  /* Slightly more pronounced hover effect */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  /* Enhanced shadow on hover */
}

.challenge-stats {
  margin-top: 2rem;
}

.how-it-works-card,
.user-info-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step-card {
  padding: 1.5rem;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.step-description {
  color: #666;
  line-height: 1.5;
}

.start-section {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.start-button {
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: none;
}

.completed-game-section {
  padding: 1rem 0;
}

.completion-message {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid #e8f5e8;
}

.completed-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

.invite-others {
  background: #fafafa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
}

.invite-button {
  font-weight: 500;
}

.currency-symbol {
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.selected-currency {
  font-weight: 500;
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 0;
  }

  .start-section {
    padding: 2rem 1rem;
  }

  .start-button {
    width: 100%;
    max-width: 300px;
  }

  .hpg-logo-img {
    height: 60px;
    /* Smaller size for mobile */
  }

  .welcome-icon {
    width: 100px;
    height: 100px;
  }

  .completed-actions {
    flex-direction: column;
    align-items: center;
  }

  .completed-actions .q-btn {
    width: 100%;
    max-width: 280px;
  }

  .completion-message {
    padding: 1.5rem;
  }

  .invite-others {
    padding: 1rem;
  }
}
</style>
